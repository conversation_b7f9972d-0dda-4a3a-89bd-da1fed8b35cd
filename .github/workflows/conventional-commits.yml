name: "Conventional Commits"

on:
  pull_request:
    types:
      - opened
      - edited
      - synchronize

permissions:
  contents: read

jobs:
  main:
    permissions:
      pull-requests: read
      statuses: write
    name: Validate PR Title
    runs-on: ubuntu-latest
    steps:
      - name: semantic-pull-request
        uses: amannn/action-semantic-pull-request@v6
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          validateSingleCommit: false
