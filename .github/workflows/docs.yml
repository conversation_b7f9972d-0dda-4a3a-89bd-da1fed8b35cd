name: Docs Build and Deploy

on:
  push:
    branches:
      - main
    paths:
      - ".github/workflows/docs.yml"
      - "requirements-docs.txt"
      - "mkdocs.yml"
      - "docs/**"
  pull_request:
    branches:
      - main
    paths:
      - ".github/workflows/docs.yml"
      - "requirements-docs.txt"
      - "mkdocs.yml"
      - "docs/**"

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      actions: read

    if: github.repository == 'google-agentic-commerce/AP2'

    steps:
      - name: Checkout Code
        uses: actions/checkout@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Configure Git Credentials
        run: |
          git config --global user.name github-actions[bot]
          git config --global user.email 41898282+github-actions[bot]@users.noreply.github.com

      - name: Setup Python
        uses: actions/setup-python@v6
        with:
          python-version: 3.13

      - name: Restore pip cache
        uses: actions/cache@v4
        with:
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements-docs.txt') }}
          path: ~/.cache/pip
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install documentation dependencies
        run: pip install -r requirements-docs.txt

      - name: Build Documentation (PR Check)
        if: github.event_name == 'pull_request'
        run: mkdocs build

      - name: Deploy development version from main branch
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        run: |
          mkdocs gh-deploy
