[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "ap2"
version = "0.1"
description = "Agent Payments Protocol"
dependencies = [
  "pydantic"
]
keywords = ["payments", "a2a", "ap2"]
license = { text = "Apache-2.0" }
readme = "README.md"
requires-python = ">=3.10"

[tool.setuptools.packages.find]
where = ["src"]

[tool.uv.workspace]
members = ["samples/python"]
