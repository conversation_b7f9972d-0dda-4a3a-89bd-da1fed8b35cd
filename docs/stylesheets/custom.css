/**
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.md-grid {
  max-width: 80%;
}

.footer {
  padding-bottom: 30vh;
}

.centered-logo-text-group {
  display: inline-flex;
  align-items: center;
  gap: 1.5em;
  margin-bottom: 0.5em;
  vertical-align: middle;
}

.centered-logo-text-group img {
  height: auto;
}

.centered-logo-text-group h1 {
  margin: 0;
  text-align: left;
}

.install-command-container {
  max-width: 600px;
  margin: 2.5em auto;
  padding: 1.5em 2em;
  background-color: var(--md-code-bg-color, #f5f5f5);
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 3px 6px rgb(0 0 0 / 5%);
  border-left: 5px solid var(--md-primary-fg-color, #526cfe);
  margin-top: 30px;
}

.install-command-container p {
  font-size: 1.1em;
  color: var(--md-default-fg-color);
  margin-bottom: -10px;
  margin-top: -10px;
}

.install-command-container p code {
  font-size: 1.1em;
  font-weight: 600;
  padding: 0.3em 0.6em;
  background-color: var(--md-code-fg-color--light);
  border-radius: 4px;
  display: inline-block;
  line-height: 1.4;
}

.announce .md-button {
  font-size: 0.8em;
  padding: 0.3em 1em;
  margin-left: 0.5em;
}

.thumb img {
  height: 200px;
  max-height: 200px;
}
