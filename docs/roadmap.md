# AP2 Roadmap

The development and rollout of the protocol are envisioned in a phased approach,
allowing the ecosystem to build, test, and adopt capabilities incrementally.

**V0.1: (September 2025)** The initial specification focuses on establishing the
core architecture and enabling the most common use cases. Key features include:
Support for "pull" payment methods (e.g., credit/debit cards) Well-defined data
payloads to support transparent accountability based on the VDC framework Support
for human-present scenarios Support for user and merchant-initiated step-up
challenges Detailed sequence diagram and reference implementation using A2A
protocol

- [x] AP2 specifications v0.1 (human present, pull payments)
- [ ] AP2 A2A extension v0.1
- [ ] AP2 MCP server v0.1
- [ ] AP2 python SDK v0.1
- [ ] AP2 android SDK v0.1

**V1.x:** Subsequent versions will expand the protocol's capabilities based on
community feedback and evolving needs. Potential areas of focus include: Full
support for "push" payments and all payment methods (e.g., real-time bank
transfers, e-wallets etc.) Standardized flows for recurring payments and
subscriptions Support for human-not-present scenarios Detailed sequence diagrams
for MCP-based implementations

**Long-Term Vision:** Longer term, we plan the protocol to incorporate more
intelligence and flexibility, including: Native support for complex,
multi-merchant transaction topologies Support for real-time negotiations between
buyer and seller agents We believe that a collaborative approach is essential to
creating a protocol that is robust, secure, and meets the diverse needs of the
entire ecosystem. We actively seek feedback and critique on the github
repository through issues and discussions.
