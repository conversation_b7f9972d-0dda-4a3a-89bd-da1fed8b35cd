# Glossary

| Term                                   | Acronym                                 | Definition                                                                                                                                                                                                                                                                                               | Synonym / Related                                                                                                                                          |
| :------------------------------------- | :-------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Agent Payments Protocol                | AP2                                     | An open protocol designed to enable AI agents to securely interoperate and complete payments autonomously.                                                                                                                                                                                               |                                                                                                                                                            |
| Agent2Agent Protocol                   | [A2A](https://a2a-protocol.org/latest/) | An open standard for secure, collaborative communication and task management between diverse AI agents, regardless of their underlying frameworks. <br><br>**Context:** AP2 can be implemented as an extension of A2A to facilitate financial transactions, with a focus on high-trust agentic payments. |                                                                                                                                                            |
| Agentic payments                       |                                         | Payment flows in which autonomous AI Agents are involved.                                                                                                                                                                                                                                                |                                                                                                                                                            |
| Credentials Provider                   | CP                                      | A secure entity, like a digital wallet, responsible for managing and executing the user's payment and identity credentials.                                                                                                                                                                              |                                                                                                                                                            |
| Deterministic cryptographical proof    |                                         | A unique mathematical proof that could be linked to a hardware device.                                                                                                                                                                                                                                   |                                                                                                                                                            |
| Dynamic Linking                        |                                         | A core requirement for Strong Customer Authentication (SCA), involving the inclusion of specific transaction details within a Payment Credential to link the SCA to that transaction. <br><br>**Context:** Ensures the payer explicitly consents to transaction details like amount and payee.           |                                                                                                                                                            |
| Merchant Endpoint (Or Remote Endpoint) | ME, RE                                  | The web interface or AI agent representing the seller, showcasing products and negotiating the cart.                                                                                                                                                                                                     |                                                                                                                                                            |
| Merchant Payment Processor Endpoint    | MPP                                     | The entity responsible for constructing and sending the transaction authorization message to the payment ecosystem.                                                                                                                                                                                      |                                                                                                                                                            |
| Model Context Protocol                 | MCP                                     | A protocol standardizing how AI models and agents connect to and interact with external resources like tools, APIs, and data sources.                                                                                                                                                                    |                                                                                                                                                            |
| Payee                                  |                                         | A person or entity who is the intended recipient of funds from a payment transaction. <br><br>**Context:** Can request attestations along with payment confirmation.                                                                                                                                     | Merchant, Creditor Name                                                                                                                                    |
| Payer                                  |                                         | A person or entity who holds a payment account, allows a payment order from that account. <br><br>**Context:** Initiates payment orders.                                                                                                                                                                 | User, Holder                                                                                                                                               |
| Payment Agent                          |                                         | A proposed key role in agent payments responsible for selecting the ideal payment method, validating payment details, and handling errors. <br><br>**Context:** A Payment Agent is not a necessary requirement to participate in the protocol. MCP-based endpoints can also play similar roles.          |                                                                                                                                                            |
| Payment Contracts                      |                                         | "Mandates" or "Payment Contracts" that capture a user's specific instructions to their agent. <br><br>**Context:** Define principles for safe, secure, and high-trust agentic payments. They can be global or transaction-level.                                                                         | Mandates, Shopping Mandate                                                                                                                                 |
| Payment Credential                     |                                         | A credential or instrument (or reference to one) which can be charged towards a payment.                                                                                                                                                                                                                 | Electronic Attestation of Attributes (EAA), Payment Wallet Attestation (PWA), Digital Payment Credentials (DPC), Payment Means Attestation, SPC Credential |
| Payment Request API (W3C Standard)     |                                         | A web API that acts as an intermediary between a merchant, user, and payment method provider to streamline the payment experience.                                                                                                                                                                       |                                                                                                                                                            |
| Strong Customer Authentication         | SCA                                     | A process required by regulatory frameworks for online identification and transaction initiation in financial services. <br><br>**Context:** Central to the Payments Rulebook, ensuring security and dynamic linking of transactions.                                                                    | Strong User Authentication, Integrated SCA                                                                                                                 |
| User                                   |                                         | The human initiating the task and providing financial authority.                                                                                                                                                                                                                                         |                                                                                                                                                            |
| User Agent a.k.a. Shopping Agent       | UA, SA                                  | The AI surface interacts directly with the user, understanding their needs, and coordinating the purchase.                                                                                                                                                                                               |                                                                                                                                                            |
| Verifiable digital credential          | VDC                                     | An Issuer-signed credential (i.e., a set of Claims) whose authenticity can be verified. <br><br>**Context:** Typically bound to a cryptographic key and used in the issuer-holder-verifier model.                                                                                                        | Digital credential, Verifiable Credential                                                                                                                                         |
| Verifiable Presentation                | VP                                      | A presentation of one or more VDCs that includes a cryptographic proof of holder binding, created in response to a request from a Verifier. <br><br>**Context:** Allows a Verifier to confirm that the Holder intended the presentation for them and may reveal only a subset of claims.                  |                                                                                                                                                            |
