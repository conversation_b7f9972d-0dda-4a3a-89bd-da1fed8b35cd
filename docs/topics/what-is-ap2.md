# AP2 Overview

The evolution of digital interaction is entering a new phase, moving beyond
direct human interaction with websites and apps to conversational and delegated
task execution by AI agents. In commerce, this means agents will manage
everything from routine purchases to complex product research and price
negotiation.

This new era of **agentic commerce** promises a hyper-personalized and
frictionless shopping experience for users while providing merchants with new,
intelligent channels to reach customers.

## The Foundational Gap: A Crisis of Trust

Despite its promise, the rise of agentic commerce exposes a critical
vulnerability: today's payment systems were designed for direct human
interaction. When an autonomous agent initiates a payment, fundamental questions
arise that current systems cannot answer:

- **Authorization & Auditability**: What verifiable proof exists that the user
    granted the agent specific authority to make a particular purchase?
- **Authenticity of Intent**: How can a merchant be certain that an agent's
    request accurately reflects the user's true intent, free from errors or AI
    "hallucinations"?
- **Accountability**: In the event of a fraudulent or erroneous transaction,
    who is accountable? The user, the agent's developer, the merchant, or the
    payment network?

This ambiguity creates a crisis of trust that could hinder adoption, expose
merchants to fraud, and lead users to hesitate before delegating financial
authority to agents.

## The Risk of a Fragmented Ecosystem

Without a common standard, the industry risks developing a patchwork of
proprietary, closed-loop solutions. This would create a confusing experience for
users, high integration costs for merchants (especially small businesses), and
prevent the payments ecosystem from consistently mitigating fraud.

## The Solution: An Open, Interoperable Protocol

The **Agent Payments Protocol (AP2)** is proposed as a non-proprietary, open
extension for existing agent-to-agent (A2A) and model-context (MCP) protocols.

It creates a common, trusted language for all participants, ensuring that any
compliant agent can securely transact with any compliant merchant. By
establishing a secure and reliable framework for AI-driven commerce, AP2 paves
the way for a competitive and innovative marketplace.

![Agent Payments Protocol Graphic](../assets/ap2_graphic.png)
