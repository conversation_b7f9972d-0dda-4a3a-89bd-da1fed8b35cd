# Python Samples for the Agent Payments Protocol AP2

This directory contains Python samples demonstrating how to use AP2.

## Getting Started

*   **Explore Scenarios**: To understand what these samples can do, see the
    [scenarios](./scenarios) directory for detailed examples.
*   **Review the Code**: Dive into the implementation by reviewing the code in
    the [src](./src) directory.
*   **All Samples**: Return to the main [samples](..) directory to see examples
    in other languages.

### Prerequisites

- Python 3.10+
- `uv`

### Installation

Set up your virtual environment and install packages:

```
uv sync
```

(Note: Each scenario has a run.sh script that will do this automatically.)